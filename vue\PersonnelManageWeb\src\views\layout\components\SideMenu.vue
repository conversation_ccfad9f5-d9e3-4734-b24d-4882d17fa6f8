<!--
 * @Descripttion: 侧边菜单
 * @Author: 曾元球
 * @Date: 2019-12-03 11:34:13
 * @LastEditors: 赖鹏辉
 * @LastEditTime: 2020-06-04 15:05:40
 -->

<template>
  <el-menu
    background-color="#384562"
    text-color="#c4c8d0"
    active-text-color="#ffffff"
    :collapse="collapse"
    :default-active="activeMenu"
    unique-opened
    menu-list
  >
    <!-- 嵌套一层解决ElmentUI报错 Maximum call stack size exceeded -->
    <li
      menu-item
      v-for="menu in menus"
      :key="menu.menuId"
    >
      <menu-item :menu="menu.children && menu.children.length === 1 ? menu.children[0]: menu"></menu-item>
    </li>
  </el-menu>
</template>

<script>
import MenuItem from './MenuItem.vue'

export default {
  name: 'SideMenu',
  components: { MenuItem },
  props: {
    collapse: Boolean
  },

  computed: {
    menus () {
      const menus = this.$store.state.user.menus
      console.log('当前菜单数据:', menus)
      return menus
    },
    activeMenu () {
      let { path, meta } = this.$route
      return meta.activeMenu || path
    }
  }
}
</script>

<style lang="scss" scoped>
.el-menu {
  flex: 0 0 240px;
  width: 240px;
  border: 0 none;
}
.el-menu.el-menu--collapse {
  flex: 0 0 60px;
  width: 60px;
}

[menu-list]::v-deep.el-menu {
  // 背景色、字体颜色(使用 !important 主要是为了覆盖行内样式)
  .is-opened {
    background: #2c3853 !important;
    .el-submenu__title,
    .el-menu-item {
      background: #2c3853 !important;
      &:hover {
        background: #2c3853 !important;
      }
      &.is-active {
        background: #416eff !important;
      }
    }
  }
  .is-active > .el-submenu__title {
    color: #fff !important;
    i {
      color: #fff;
    }
  }
  .el-submenu__title:hover {
    background: transparent !important;
    color: #fff !important;
  }
  .el-menu-item:hover {
    background: transparent !important;
    color: #fff !important;
  }
  .el-menu-item.is-active {
    background: #416eff !important;
  }

  &
    > [menu-item]
    > [menu-ul]
    > .is-active:not(.is-opened)
    > .el-submenu__title {
    background: #416eff !important;
    color: #fff !important;
    i {
      color: #fff;
    }
  }

  // 重写折叠的样式
  &.el-menu--collapse {
    & > [menu-item] {
      & > [menu-ul] > .is-active.is-opened > .el-submenu__title {
        background: #416eff !important;
        color: #fff !important;
      }
      span,
      .el-submenu__icon-arrow {
        display: none;
      }
      .el-submenu__title {
        span,
        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }
    .el-menu-item:hover {
      background: #2c3853 !important;
    }
    .el-menu-item.is-active {
      background: #416eff !important;
    }
  }

  i {
    color: #c4c8d0;
  }
}

[menu-item]::v-deep > ul {
  & > .el-submenu {
    & > .el-submenu__title {
      font-size: 16px;
    }
  }
  & > .el-menu-item {
    font-size: 16px;
  }
}
</style>
