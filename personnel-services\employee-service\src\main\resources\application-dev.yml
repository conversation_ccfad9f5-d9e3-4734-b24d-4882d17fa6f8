eureka:
  client:
    service-url:
      defaultZone: http://127.0.0.1:1112/eureka
  instance:
    metadata-map:
      zone: personnel

spring:
  datasource:
    url: *****************************************************************************************************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      maximum-pool-size: 15
      minimum-idle: 3
      connection-timeout: 60000
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-test-query: SELECT 1
      validation-timeout: 3000
      leak-detection-threshold: 60000
      pool-name: EmployeeServiceDevHikariCP
  redis:
    host: 127.0.0.1
    port: 6379

# 路径配置
base:
  upload:
    path: /bojun/bojunFile/
    url: http://*************:8808/fileupload/
  http:
    url: http://*************:8095/

personnel:
  base:
    upload:
      path: D:/bojunFile/personnel/

# 企业微信配置
bojun:
  corp:
    id: ww27980c77956147a4

AgentId: 1000047
Secret: eaD5tAwht5rkLT45f50oPZW0SHJbn8FKQkFlSK_vuIM

# 微信授权配置
auth_url: "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect"
redirect_uri: "https://vip.bj-ihealthcare.com/doctorSchedulingH5"
test_user_id: "000800"

# 邮件配置
email:
  smtp:
    host: smtp.qq.com
  account: <EMAIL>
  password: ekcrwyjmmtahbhfi
