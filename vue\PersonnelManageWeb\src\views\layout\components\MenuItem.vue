<!--
 * @Descripttion: 菜单项
 * @Author: 曾元球
 * @Date: 2019-12-03 11:34:13
 * @LastEditors: 钟小苇
 * @LastEditTime: 2020-07-30 15:28:41
 -->

<template>
  <ul menu-ul>
    <el-submenu
      v-if="menu.children && menu.children.length > 1"
      :index="menu.menuUrl || menu.menuId"
    >
      <template slot="title">
        <!--
          如果非 叶子 菜单想点击响应跳转可包裹一层
          <div @click="处理跳转操作"></div>
        -->
        <!-- <div class="submenu-click"></div> -->
        <label v-if="menu.icon" class="iconfont" :class="menu.icon"></label>
        <span class="menu-text">{{ menu.menuName }}</span>
      </template>
      <menu-item
        v-for="subMenu in menu.children"
        :menu="subMenu"
        :key="subMenu.menuId"
      ></menu-item>
    </el-submenu>
    <el-menu-item
      :index="menu.menuUrl || menu.menuId"
      v-else
      @click="jumpTo(menu)"
    >
      <label v-if="menu.icon" class="iconfont" :class="menu.icon"></label>
      <span slot="title" class="menu-text">{{ menu.menuName }}</span>
      <!-- 调试信息 -->
      <span v-if="menu.menuName === '实习生管理'" style="color: red; font-size: 10px;">
        (URL: {{ menu.menuUrl }})
      </span>
    </el-menu-item>
  </ul>
</template>

<script>
export default {
  name: 'MenuItem',
  props: {
    menu: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    jumpTo ({ menuUrl }) {
      console.log('菜单点击:', { menuUrl, currentPath: this.$route.path })
      if (menuUrl === this.$route.path) {
        console.log('当前已在该页面，跳过跳转')
        return
      }
      if (menuUrl) {
        console.log('准备跳转到:', menuUrl)
        this.$router.push(menuUrl).catch(err => {
          console.error('路由跳转失败:', err)
        })
      } else {
        console.warn('菜单URL为空')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.menu-text {
  padding-left: 10px;
}
</style>
